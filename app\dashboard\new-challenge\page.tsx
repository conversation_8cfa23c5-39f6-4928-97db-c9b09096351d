"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/sheet"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import {
  TrendingUp,
  Gift,
  Moon,
  Sun,
  X,
  Copy,
  Clock,
  CreditCard,
  Bitcoin,
  ChevronDown,
  ChevronRight,
  Check,
  Minus,
  Plus
} from "lucide-react"
import Link from "next/link"

const challengeTypeData = {
  "1-phase": {
    name: "One-Step",
    accountSizes: [
      { size: "$1K", price: "10", discountedPrice: "8" },
      { size: "$3K", price: "17", discountedPrice: "13" },
      { size: "$5K", price: "24", discountedPrice: "19" },
      { size: "$10K", price: "42", discountedPrice: "33" },
      { size: "$25K", price: "73", discountedPrice: "58" },
      { size: "$50K", price: "138", discountedPrice: "55" },
      { size: "$100K", price: "193", discountedPrice: "77" },
      { size: "$200K", price: "263", discountedPrice: "105" },
      { size: "$500K", price: "438", discountedPrice: "175" }
    ]
  },
  "2-phase": {
    name: "Two-Step",
    accountSizes: [
      { size: "$1K", price: "8", discountedPrice: "6" },
      { size: "$3K", price: "14", discountedPrice: "11" },
      { size: "$5K", price: "22", discountedPrice: "17" },
      { size: "$10K", price: "32", discountedPrice: "25" },
      { size: "$25K", price: "62", discountedPrice: "49" },
      { size: "$50K", price: "105", discountedPrice: "42" },
      { size: "$100K", price: "163", discountedPrice: "65" },
      { size: "$200K", price: "225", discountedPrice: "90" },
      { size: "$500K", price: "338", discountedPrice: "135" }
    ]
  },
  "hft": {
    name: "HFT",
    accountSizes: [
      { size: "$1K", price: "15", discountedPrice: "12" },
      { size: "$3K", price: "28", discountedPrice: "22" },
      { size: "$5K", price: "43", discountedPrice: "34" },
      { size: "$10K", price: "64", discountedPrice: "51" },
      { size: "$25K", price: "112", discountedPrice: "89" },
      { size: "$50K", price: "213", discountedPrice: "85" },
      { size: "$100K", price: "288", discountedPrice: "115" },
      { size: "$200K", price: "420", discountedPrice: "168" },
      { size: "$500K", price: "675", discountedPrice: "270" }
    ]
  },
  "instant": {
    name: "Instant",
    accountSizes: [
      { size: "$1K", price: "56", discountedPrice: "45" },
      { size: "$3K", price: "106", discountedPrice: "85" },
      { size: "$5K", price: "169", discountedPrice: "135" },
      { size: "$10K", price: "244", discountedPrice: "195" },
      { size: "$25K", price: "482", discountedPrice: "385" },
      { size: "$50K", price: "938", discountedPrice: "375" },
      { size: "$100K", price: "1875", discountedPrice: "750" },
      { size: "$200K", price: "3125", discountedPrice: "1250" },
      { size: "$500K", price: "4625", discountedPrice: "1850" }
    ]
  }
} as const

export default function NewChallengePage() {
  const [selectedChallengeType, setSelectedChallengeType] = useState<keyof typeof challengeTypeData>("1-phase")
  const [isDarkMode, setIsDarkMode] = useState(false)
  const [isPaymentSheetOpen, setIsPaymentSheetOpen] = useState(false)
  const [selectedPlan, setSelectedPlan] = useState({
    size: "$25K",
    price: "155",
    discountedPrice: "124",
    platform: "MT5",
    swapType: "Swap"
  })
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState("")
  const [agreedToTerms, setAgreedToTerms] = useState(false)
  const [countdown, setCountdown] = useState({ hours: 85, minutes: 10, seconds: 54 })

  const challengeTypes = [
    {
      id: "1-phase",
      name: "1 Phase",
      active: selectedChallengeType === "1-phase"
    },
    {
      id: "2-phase",
      name: "2 Phase",
      active: selectedChallengeType === "2-phase"
    },
    { 
      id: "hft", 
      name: "HFT", 
      active: selectedChallengeType === "hft" 
    },
    { 
      id: "instant", 
      name: "Instant", 
      active: selectedChallengeType === "instant" 
    }
  ]



  const getTradingParameters = (challengeType: string) => {
    switch (challengeType) {
      case "1-phase":
        return [
          { parameter: "Profit Target", value: "10%" },
          { parameter: "Daily Drawdown Limit", value: "4%" },
          { parameter: "Overall Drawdown Limit", value: "8%" },
          { parameter: "Minimum Trading Days", value: "5" },
          { parameter: "Leverage", value: "1:100" },
          { parameter: "Profit Split", value: "80%" },
          { parameter: "News Trading", value: "Allowed" },
          { parameter: "Overnight Trading", value: "Allowed" },
          { parameter: "Weekend Holding", value: "Allowed" },
          { parameter: "Consistency Rule", value: "None" },
          { parameter: "Withdrawals", value: "Weekly" },
          { parameter: "EA (Expert Advisor)", value: "Allowed" },
          { parameter: "HFT", value: "Not Allowed" }
        ]
      case "2-phase":
        return [
          { parameter: "Profit Target (Phase 1)", value: "10%" },
          { parameter: "Profit Target (Phase 2)", value: "5%" },
          { parameter: "Daily Drawdown Limit", value: "5%" },
          { parameter: "Overall Drawdown Limit", value: "10%" },
          { parameter: "Minimum Trading Days", value: "5" },
          { parameter: "Leverage", value: "1:100" },
          { parameter: "Profit Split", value: "80%" },
          { parameter: "News Trading", value: "Allowed" },
          { parameter: "Overnight Trading", value: "Allowed" },
          { parameter: "Weekend Holding", value: "Allowed" },
          { parameter: "Consistency Rule", value: "None" },
          { parameter: "Withdrawals", value: "Weekly" },
          { parameter: "EA (Expert Advisor)", value: "Allowed" },
          { parameter: "HFT", value: "Not Allowed" }
        ]
      case "hft":
        return [
          { parameter: "Profit Target", value: "8%" },
          { parameter: "Daily Drawdown Limit", value: "3%" },
          { parameter: "Overall Drawdown Limit", value: "6%" },
          { parameter: "Minimum Trading Days", value: "1" },
          { parameter: "Leverage", value: "1:100" },
          { parameter: "Profit Split", value: "90%" },
          { parameter: "News Trading", value: "Allowed" },
          { parameter: "Overnight Trading", value: "Allowed" },
          { parameter: "Weekend Holding", value: "Not Allowed" },
          { parameter: "Consistency Rule", value: "35% Profit Consistency Rule" },
          { parameter: "Withdrawals", value: "Bi-Weekly" },
          { parameter: "HFT Bot", value: "Allowed" }
        ]
      case "instant":
        return [
          { parameter: "Daily Drawdown Limit", value: "2%" },
          { parameter: "Overall Drawdown Limit", value: "4%" },
          { parameter: "Leverage", value: "1:100" },
          { parameter: "Profit Split", value: "90%" },
          { parameter: "News Trading", value: "Not Allowed" },
          { parameter: "Overnight Trading", value: "Allowed" },
          { parameter: "Weekend Holding", value: "Not Allowed" },
          { parameter: "Consistency Rule", value: "25% Profit Consistency Rule" },
          { parameter: "Withdrawals", value: "Bi-Weekly" },
          { parameter: "EA/HFT", value: "Not Allowed" }
        ]
      default:
        return [
          { parameter: "Profit Target", value: "10%" },
          { parameter: "Daily Drawdown Limit", value: "4%" },
          { parameter: "Overall Drawdown Limit", value: "8%" },
          { parameter: "Minimum Trading Days", value: "5" },
          { parameter: "Leverage", value: "1:100" },
          { parameter: "Profit Split", value: "80%" },
          { parameter: "News Trading", value: "Allowed" },
          { parameter: "Overnight Trading", value: "Allowed" },
          { parameter: "Weekend Holding", value: "Allowed" },
          { parameter: "Consistency Rule", value: "None" },
          { parameter: "Withdrawals", value: "Weekly" },
          { parameter: "EA (Expert Advisor)", value: "Allowed" },
          { parameter: "HFT", value: "Not Allowed" }
        ]
    }
  }

  const paymentMethods = [
    {
      id: "cards",
      name: "International Cards",
      icon: CreditCard,
      description: "Visa, Mastercard, American Express",
      fees: "0%"
    },
    {
      id: "skrill",
      name: "Skrill",
      icon: CreditCard,
      description: "Skrill",
      fees: "+3% Provider Fees"
    },
    {
      id: "crypto",
      name: "Crypto",
      icon: Bitcoin,
      description: "Bitcoin, Ethereum, USDT",
      fees: "+3% Provider Fees"
    }
  ]

  const addOns = [
    {
      id: "lifetime",
      name: "Lifetime Payout 95%",
      price: "+30%",
      description: "Get 95% profit split for life"
    },
    {
      id: "no-minimum",
      name: "No Minimum Trading Days",
      price: "+20%",
      description: "Remove minimum trading day requirement"
    },
    {
      id: "bi-weekly",
      name: "Bi-Weekly Payout",
      price: "+15%",
      description: "Get paid every 2 weeks"
    },
    {
      id: "overall-loss",
      name: "Overall Loss Limit 10%",
      price: "+25%",
      description: "Increase overall loss limit to 10%"
    },
    {
      id: "double-up",
      name: "Double Up",
      price: "+40%",
      description: "Double your account size"
    }
  ]

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode)
    document.documentElement.classList.toggle('dark')
  }

  const handleBuyNow = (size: string, price: string, discountedPrice: string) => {
    setSelectedPlan({ ...selectedPlan, size, price, discountedPrice })
    setIsPaymentSheetOpen(true)
  }

  const copyCouponCode = () => {
    navigator.clipboard.writeText('LITE5%')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-slate-950 dark:via-gray-900 dark:to-slate-950 text-gray-900 dark:text-white overflow-hidden relative transition-colors duration-500">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-400/10 dark:bg-blue-400/5 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-cyan-400/10 dark:bg-cyan-400/5 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-indigo-400/5 dark:bg-indigo-400/3 rounded-full blur-3xl"></div>
      </div>

      {/* Navigation */}
      <nav className="fixed top-4 left-4 right-4 z-40 flex items-center justify-between bg-transparent backdrop-blur-sm border border-gray-200/30 dark:border-white/10 rounded-2xl px-3 py-2 shadow-sm">
        <Link href="/" className="flex items-center gap-2 group">
          <div className="w-8 h-8 md:w-10 md:h-10 rounded-xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
            <TrendingUp className="w-4 h-4 md:w-5 md:h-5 text-white" />
          </div>
          <div className="hidden sm:block">
            <span className="text-lg md:text-xl font-bold bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent">
              FxThrone
            </span>
          </div>
        </Link>

        <div className="flex items-center gap-2 md:gap-4">
          <Button
            variant="ghost"
            onClick={toggleTheme}
            className="text-xs md:text-sm font-light text-gray-700 dark:text-white/80 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/30 dark:hover:bg-white/5 transition-all duration-300 px-2 py-1 rounded-lg group"
          >
            <div className="group-hover:rotate-180 transition-transform duration-500">
              {isDarkMode ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
            </div>
          </Button>
          <Link href="/dashboard">
            <Button variant="ghost" className="text-xs md:text-sm font-medium text-gray-700 dark:text-white/80 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/30 dark:hover:bg-white/5 transition-all duration-300 px-3 py-2 rounded-lg">
              Dashboard
            </Button>
          </Link>
        </div>
      </nav>

      {/* Main Content */}
      <main className="relative z-10 pt-24">
        <section className="py-16 md:py-24">
          <div className="max-w-7xl mx-auto px-8 md:px-12 lg:px-16 relative z-10">
            {/* Header */}
            <div className="text-center mb-8">
              <h2 className="text-xl md:text-2xl lg:text-3xl font-bold mb-3 leading-tight tracking-tight">
                Choose Your
                <span className="block bg-gradient-to-r from-blue-600 via-cyan-600 to-indigo-600 dark:from-blue-400 dark:via-cyan-400 dark:to-indigo-400 bg-clip-text text-transparent">
                  Challenge
                </span>
              </h2>

              {/* Coupon Code Advertisement */}
              <div className="max-w-2xl mx-auto mb-4">
                <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border-2 border-green-200 dark:border-green-700 rounded-2xl p-3 shadow-lg">
                  <div className="flex items-center justify-center gap-1 mb-1">
                    <Gift className="w-4 h-4 text-green-600" />
                    <span className="text-xs font-bold text-green-700 dark:text-green-400">LIMITED TIME OFFER!</span>
                  </div>
                  <p className="text-gray-700 dark:text-gray-300 mb-2 text-xs">
                    Get 50% OFF on all challenges! Use coupon code at checkout.
                  </p>
                  <div className="bg-white dark:bg-gray-800 border-2 border-dashed border-green-300 dark:border-green-600 rounded-lg p-1 inline-block">
                    <div className="flex items-center gap-1">
                      <span className="text-xs text-gray-600 dark:text-gray-400">Coupon Code:</span>
                      <span className="text-sm font-bold text-green-600 dark:text-green-400 tracking-wider">SAVE50</span>
                      <Button
                        size="sm"
                        variant="outline"
                        className="ml-1 text-xs border-green-300 text-green-600 hover:bg-green-50 dark:border-green-600 dark:text-green-400 dark:hover:bg-green-900/20"
                        onClick={() => navigator.clipboard.writeText('SAVE50')}
                      >
                        Copy
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Challenge Type Selectors */}
            <div className="flex flex-col items-center mb-8">
              <div className="bg-white dark:bg-gray-900 rounded-2xl p-2 md:p-3 shadow-lg border border-gray-200 dark:border-gray-700 mb-4 w-full max-w-4xl">
                {/* Challenge Options Row */}
                <div className="flex items-center justify-center gap-1 md:gap-2 mb-3 flex-wrap">
                  {challengeTypes.map((type) => (
                    <div key={type.id} className="flex flex-col items-center">
                      <Button
                        variant={type.active ? "default" : "ghost"}
                        className={`rounded-xl px-1 md:px-3 py-1 text-xs font-semibold transition-all duration-300 ${
                          type.active
                            ? "bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg hover:from-blue-700 hover:to-cyan-700"
                            : "text-gray-600 dark:text-white/70 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800"
                        }`}
                        onClick={() => {
                          setSelectedChallengeType(type.id as keyof typeof challengeTypeData)
                        }}
                      >
                        {type.name}
                      </Button>
                    </div>
                  ))}
                </div>
              </div>

              <Button className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white px-3 md:px-4 py-1 md:py-2 rounded-xl text-xs font-semibold hover:scale-105 transition-all duration-300 shadow-lg">
                Get Funded
              </Button>
            </div>

            {/* Pricing Table */}
            <div className="bg-white dark:bg-gray-900 rounded-3xl shadow-2xl overflow-hidden border border-gray-200 dark:border-gray-700">
              <div className="overflow-x-auto">
                <table className="w-full min-w-[600px]">
                  <thead>
                    <tr className="bg-gradient-to-r from-blue-100 to-cyan-100 dark:from-blue-900/20 dark:to-cyan-900/20">
                      <th className="text-left p-2 md:p-3 font-bold text-gray-900 dark:text-white text-xs">
                        Trading Parameters
                      </th>
                      {challengeTypeData[selectedChallengeType]?.accountSizes.map((account, index) => (
                        <th key={index} className="text-center p-2 md:p-3 font-bold text-gray-900 dark:text-white text-xs">
                          {account.size}
                        </th>
                      ))}
                    </tr>
                    {/* Buy Now Buttons Row */}
                    <tr className="bg-white dark:bg-gray-900">
                      <td className="p-2 md:p-3 font-bold text-gray-900 dark:text-white text-xs">
                        Buy Now
                      </td>
                      {challengeTypeData[selectedChallengeType]?.accountSizes.map((account, index) => (
                        <td key={index} className="text-center p-2 md:p-3">
                          <Button 
                            className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white px-2 md:px-3 py-1 rounded-xl text-xs font-semibold transition-all duration-300 hover:scale-105 shadow-md"
                            onClick={() => handleBuyNow(account.size, account.price, account.discountedPrice)}
                          >
                            Buy Now
                          </Button>
                        </td>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {getTradingParameters(selectedChallengeType).map((param, index) => (
                      <tr
                        key={index}
                        className={`border-b border-gray-100 dark:border-gray-800 ${
                          index % 2 === 0 ? "bg-gray-50 dark:bg-gray-800/50" : ""
                        }`}
                      >
                        <td className="p-2 md:p-3 font-semibold text-gray-900 dark:text-white text-xs">
                          {param.parameter}
                        </td>
                        {challengeTypeData[selectedChallengeType]?.accountSizes.map((_, accountIndex) => (
                          <td key={accountIndex} className="text-center p-2 md:p-3 text-gray-700 dark:text-white/70 text-xs font-medium">
                            {param.value}
                          </td>
                        ))}
                      </tr>
                    ))}
                    {/* Price Row */}
                    <tr className="border-t-2 border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20">
                      <td className="p-2 md:p-3 font-bold text-gray-900 dark:text-white text-xs">
                        Price
                      </td>
                      {challengeTypeData[selectedChallengeType]?.accountSizes.map((account, index) => (
                        <td key={index} className="text-center p-2 md:p-3">
                          <div className="text-sm font-bold text-blue-600 dark:text-blue-400">
                            ${account.discountedPrice}
                          </div>
                          <div className="text-xs text-gray-500 line-through">
                            ${account.price}
                          </div>
                        </td>
                      ))}
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Payment Side Panel */}
      <Sheet open={isPaymentSheetOpen} onOpenChange={setIsPaymentSheetOpen}>
        <SheetContent side="right" className="w-full sm:max-w-md lg:max-w-lg xl:max-w-xl overflow-y-auto">
          <div className="h-full flex flex-col">
            {/* Header */}
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 rounded-lg overflow-hidden">
                  <img 
                    src="/placeholder-logo.png" 
                    alt="FxThrone Logo" 
                    className="w-full h-full object-cover"
                  />
                </div>
                <div>
                  <h2 className="text-xs font-bold text-gray-900 dark:text-white">
                    FxThrone: {challengeTypeData[selectedChallengeType]?.name} Challenge
                  </h2>
                  <div className="flex items-center gap-1 mt-0.5">
                    <Badge variant="secondary" className="text-xs">
                      CODE: LITE5%
                    </Badge>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={copyCouponCode}
                      className="h-3 w-3 p-0"
                    >
                      <Copy className="w-1.5 h-1.5" />
                    </Button>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-1 text-xs text-gray-600 dark:text-gray-400">
                <Clock className="w-2.5 h-2.5" />
                <span className="text-xs">{countdown.hours}h: {countdown.minutes}m: {countdown.seconds}s</span>
              </div>
            </div>

            {/* Order Summary */}
            <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl p-3 mb-3 text-white">
              <h3 className="text-xs font-bold mb-1">Total Amount to be Paid</h3>
              <div className="text-lg font-bold mb-1">${selectedPlan.discountedPrice}</div>
              <div className="text-xs text-white/80 mb-2 line-through">${selectedPlan.price}</div>
              <Button variant="outline" className="w-full border-white/30 text-white hover:bg-white/10 text-xs py-1">
                Apply Code
              </Button>
            </div>

            {/* Plan Details */}
            <Card className="mb-3">
              <CardContent className="p-3">
                <h3 className="font-bold text-gray-900 dark:text-white mb-2 text-xs">Plan Details</h3>
                <div className="space-y-1 text-xs">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Platform:</span>
                    <span className="font-medium">{selectedPlan.platform}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Account Type:</span>
                    <span className="font-medium">{selectedPlan.swapType}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Original Price:</span>
                    <span className="font-medium line-through text-gray-500">${selectedPlan.price}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Discount:</span>
                    <span className="font-medium text-green-600">-${parseInt(selectedPlan.price) - parseInt(selectedPlan.discountedPrice)}</span>
                  </div>
                  <div className="flex justify-between font-bold text-sm pt-1 border-t">
                    <span>Total Amount:</span>
                    <span className="text-green-600">${selectedPlan.discountedPrice}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Payment Methods */}
            <div className="mb-3">
              <h3 className="font-bold text-gray-900 dark:text-white mb-2 text-xs">Select a payment method</h3>
              <RadioGroup value={selectedPaymentMethod} onValueChange={setSelectedPaymentMethod}>
                <div className="space-y-1">
                  {paymentMethods.map((method) => (
                    <div key={method.id} className="flex items-center space-x-2">
                      <RadioGroupItem value={method.id} id={method.id} />
                      <Label htmlFor={method.id} className="flex-1 cursor-pointer">
                        <div className="flex items-center justify-between p-2 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800">
                          <div className="flex items-center gap-1">
                            <method.icon className="w-3 h-3 text-blue-600" />
                            <div>
                              <div className="font-medium text-xs">{method.name}</div>
                              <div className="text-xs text-gray-600 dark:text-gray-400">{method.description}</div>
                            </div>
                          </div>
                          <div className="text-xs text-gray-600 dark:text-gray-400">{method.fees}</div>
                        </div>
                      </Label>
                    </div>
                  ))}
                </div>
              </RadioGroup>
            </div>



            {/* Terms and Payment Button */}
            <div className="mt-auto space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="terms" 
                  checked={agreedToTerms}
                  onCheckedChange={(checked) => setAgreedToTerms(checked as boolean)}
                />
                <Label htmlFor="terms" className="text-xs">
                  I agree to the{" "}
                  <Link href="/terms" className="text-blue-600 hover:underline">
                    Terms of Service
                  </Link>{" "}
                  &{" "}
                  <Link href="/challenge-terms" className="text-blue-600 hover:underline">
                    Challenge Terms
                  </Link>
                </Label>
              </div>
              
              <Button 
                className="w-full bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white py-2 text-xs font-semibold"
                disabled={!selectedPaymentMethod || !agreedToTerms}
              >
                Proceed to Payment
              </Button>
              
              <div className="text-center text-xs text-gray-600 dark:text-gray-400">
                Payable Amount: ${selectedPlan.discountedPrice}
              </div>
            </div>


          </div>
        </SheetContent>
      </Sheet>
    </div>
  )
}